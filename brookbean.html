<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON> & Bean - Case Study | Jaykee Aba-a</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700|space-grotesk:500" rel="stylesheet">
  <script src="https://unpkg.com/lucide@latest"></script>
  <style>
    body{font-family:'Inter',sans-serif;background:#0e0e10;color:#e5e7eb;}
    .animate{opacity:0;transform:translateY(40px);transition:all .8s cubic-bezier(.22,1,.36,1);}
    .animate.show{opacity:1;transform:none;}
    .divider{border-top:1px solid rgba(255,255,255,.08);}
    .outline{outline:1px solid rgba(255,255,255,.08);}
    ::selection{background:#6366f1;color:#fff;}
    a{transition:.3s;}
    a:hover{color:#a5b4fc;}
    .gradient-text{background:linear-gradient(90deg,#6366f1,#8b5cf6 60%,#ec4899);-webkit-background-clip:text;color:transparent;}
  </style>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex flex-col">
  <!-- NAV -->
  <header class="fixed top-0 inset-x-0 z-50 backdrop-blur-sm bg-black/30">
    <div class="mx-auto max-w-7xl flex items-center justify-between px-6 py-4">
      <a href="v2.html" class="text-lg tracking-tight font-semibold gradient-text">JK</a>
      <nav class="hidden sm:flex gap-6 text-sm">
        <a href="v2.html#work">Work</a><a href="v2.html#services">Services</a><a href="v2.html#about">About</a><a href="v2.html#contact">Contact</a>
      </nav>
      <a href="v2.html#contact" class="sm:block hidden text-xs font-medium px-4 py-2 outline rounded-md hover:bg-white/5">Let's talk</a>
      <button id="menuBtn" class="sm:hidden">
        <i data-lucide="menu" class="stroke-[1.5] w-6 h-6"></i>
      </button>
    </div>
    <!-- mobile -->
    <div id="mobileNav" class="sm:hidden px-6 pt-2 pb-4 space-y-2 hidden">
      <a href="v2.html#work" class="block">Work</a>
      <a href="v2.html#services" class="block">Services</a>
      <a href="v2.html#about" class="block">About</a>
      <a href="v2.html#contact" class="block">Contact</a>
    </div>
  </header>

  <!-- HERO SECTION -->
  <section class="relative px-6 pt-32 pb-16">
    <div class="mx-auto max-w-4xl">
      <a href="v2.html" class="inline-flex items-center gap-2 text-sm opacity-70 hover:opacity-100 mb-8 animate">
        <i data-lucide="arrow-left" class="stroke-[1.5] w-4 h-4"></i>
        Back to portfolio
      </a>
      <h1 class="text-4xl sm:text-5xl font-semibold tracking-tight animate delay-[100ms]">Brook & Bean</h1>
      <p class="mt-4 text-lg opacity-80 animate delay-[200ms]">Neighborhood Coffee - Locally roasted, open daily. Warm mornings, better coffee — your neighborhood cafe.</p>
      <div class="flex gap-4 mt-6 animate delay-[250ms]">
        <a href="https://brookandbean.vercel.app/" target="_blank" class="inline-flex items-center gap-2 px-4 py-2 bg-white text-black rounded-md text-sm font-medium transition-colors">
          Visit website
        </a>
        <a href="#project-details" class="inline-flex items-center gap-2 px-4 py-2 outline rounded-md text-sm font-medium hover:bg-white/5 transition-colors">
          View details <i data-lucide="arrow-down" class="stroke-[1.5] w-4 h-4"></i>
        </a>
      </div>
    </div>
  </section>

  <!-- PROJECT IMAGE -->
  <section class="px-6 pb-16">
    <div class="mx-auto max-w-4xl">
      <img src="brook.png" alt="Brook & Bean Coffee Shop" class="w-full rounded-lg outline animate delay-[300ms]">
    </div>
  </section>

  <!-- PROJECT DETAILS -->
  <section id="project-details" class="px-6 pb-24">
    <div class="mx-auto max-w-4xl grid md:grid-cols-3 gap-12">
      <!-- Project Info -->
      <div class="md:col-span-2 space-y-8">
        <div class="animate delay-[400ms]">
          <h2 class="text-2xl font-semibold mb-4">Project Overview</h2>
          <p class="text-sm opacity-80 leading-relaxed mb-4">
            Brook & Bean is a neighborhood coffee shop that opened in 2015 with a simple idea: connect the community over thoughtfully prepared coffee. The website embodies their warm, welcoming atmosphere and commitment to quality, sustainability, and local partnerships.
          </p>
          <p class="text-sm opacity-80 leading-relaxed">
            Small-batch roasts, house pastries, and friendly faces. They keep things simple: exceptional espresso, seasonal lattes, and a cozy corner for customers to pause and enjoy their coffee experience.
          </p>
        </div>

        <div class="animate delay-[500ms]">
          <h3 class="text-xl font-semibold mb-4">Key Features</h3>
          <ul class="space-y-3 text-sm">
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Menu showcase</strong> with seasonal offerings and detailed pricing</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Location and hours</strong> with accessibility and parking information</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Community engagement</strong> highlighting local partnerships and events</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Order ahead</strong> and curbside pickup functionality</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Story section</strong> about their mission, roasting process, and team</span>
            </li>
          </ul>
        </div>

        <div class="animate delay-[600ms]">
          <h3 class="text-xl font-semibold mb-4">Menu Highlights</h3>
          <div class="grid sm:grid-cols-2 gap-4 text-sm">
            <div class="p-4 outline rounded-lg">
              <h4 class="font-medium mb-2">Espresso</h4>
              <p class="opacity-70 text-xs mb-1">Single origin, pulled to order</p>
              <p class="text-indigo-400 font-medium">$3.00</p>
            </div>
            <div class="p-4 outline rounded-lg">
              <h4 class="font-medium mb-2">Seasonal Latte</h4>
              <p class="opacity-70 text-xs mb-1">Rotating flavors — ask the barista</p>
              <p class="text-indigo-400 font-medium">$4.50</p>
            </div>
            <div class="p-4 outline rounded-lg">
              <h4 class="font-medium mb-2">House Pastry</h4>
              <p class="opacity-70 text-xs mb-1">Baked fresh each morning</p>
              <p class="text-indigo-400 font-medium">$3.75</p>
            </div>
            <div class="p-4 outline rounded-lg">
              <h4 class="font-medium mb-2">Pour-Over</h4>
              <p class="opacity-70 text-xs mb-1">Single origin drip • 10oz</p>
              <p class="text-indigo-400 font-medium">$5.00</p>
            </div>
          </div>
        </div>

        <div class="animate delay-[700ms]">
          <h3 class="text-xl font-semibold mb-4">About Brook & Bean</h3>
          <p class="text-sm opacity-80 leading-relaxed mb-4">
            Brook & Bean began with a mission to create a calm, welcoming place in their neighborhood. They source beans directly from farms committed to ethical practices, roast them in small batches, and train their baristas to highlight the best characteristics of each origin.
          </p>
          <p class="text-sm opacity-80 leading-relaxed mb-4">
            <strong>Founded:</strong> 2015<br>
            <strong>Focus:</strong> Sustainability & quality<br>
            <strong>Hours:</strong> Daily • 7:00 AM — 5:00 PM
          </p>
          <p class="text-sm opacity-80 leading-relaxed">
            They roast weekly on-site in small batches to keep freshness front of mind. Their baristas are trained in espresso and brewing methods, and many are local artists and neighbors. The cafe hosts monthly open mic nights and supports local artists.
          </p>
        </div>

        <div class="animate delay-[750ms]">
          <h3 class="text-xl font-semibold mb-4">Visit Information</h3>
          <div class="grid sm:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 class="font-medium mb-2">Location & Contact</h4>
              <p class="opacity-70 mb-1">123 Maple Avenue, Unit B</p>
              <p class="opacity-70 mb-1">+1 (234) 567-890</p>
              <p class="opacity-70"><EMAIL></p>
            </div>
            <div>
              <h4 class="font-medium mb-2">Amenities</h4>
              <p class="opacity-70 mb-1">Street parking + 2-hour lot behind building</p>
              <p class="opacity-70 mb-1">Indoor & outdoor seating • Wi‑Fi available</p>
              <p class="opacity-70">A short walk from the 5 and 7 lines</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <div class="p-6 outline rounded-lg animate delay-[800ms]">
          <h3 class="font-semibold mb-4">Project Details</h3>
          <div class="space-y-3 text-sm">
            <div>
              <span class="opacity-70 block">Role:</span>
              <span>UI/UX Design & Development</span>
            </div>
            <div>
              <span class="opacity-70 block">Timeline:</span>
              <span>2 weeks</span>
            </div>
            <div>
              <span class="opacity-70 block">Year:</span>
              <span>2024</span>
            </div>
            <div>
              <span class="opacity-70 block">Location:</span>
              <span>123 Maple Avenue, Unit B</span>
            </div>
          </div>
        </div>

        <div class="p-6 outline rounded-lg animate delay-[900ms]">
          <h3 class="font-semibold mb-4">Technologies Used</h3>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-indigo-500/20 text-indigo-300 rounded-full text-xs">HTML (100.0%)</span>
          </div>
        </div>

        <div class="p-6 outline rounded-lg animate delay-[1000ms]">
          <h3 class="font-semibold mb-4">Results</h3>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="opacity-70">Performance:</span>
              <span class="font-medium text-green-400">97</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">Accessibility:</span>
              <span class="font-medium text-green-400">95</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">Best Practices:</span>
              <span class="font-medium text-green-400">96</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">SEO:</span>
              <span class="font-medium text-green-400">91</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- BACK TO PORTFOLIO -->
  <section class="px-6 pb-24">
    <div class="mx-auto max-w-4xl text-center">
      <a href="v2.html" class="inline-flex items-center gap-2 px-6 py-3 bg-white text-black rounded-md text-sm font-medium hover:bg-white/90 transition-colors animate delay-[1100ms]">
        <i data-lucide="arrow-left" class="stroke-[1.5] w-4 h-4"></i>
        Back to portfolio
      </a>
    </div>
  </section>

  <footer class="mt-auto py-10 px-6 text-center text-[11px] opacity-60">
    © <span id="year"></span> Jaykee Aba-a. Built & designed with love.
  </footer>

  <script>
    // Lucide
    lucide.createIcons({attrs:{'stroke-width':1.5}});

    // Mobile nav toggle
    const menuBtn=document.getElementById('menuBtn'), mobileNav=document.getElementById('mobileNav');
    menuBtn.addEventListener('click',()=>mobileNav.classList.toggle('hidden'));

    // Scroll animations
    const observer=new IntersectionObserver(entries=>{
      entries.forEach(e=>{if(e.isIntersecting){e.target.classList.add('show');observer.unobserve(e.target);}})
    },{threshold:.12});
    document.querySelectorAll('.animate').forEach(el=>observer.observe(el));

    // year
    document.getElementById('year').textContent=new Date().getFullYear();
  </script>

</body>
</html>
