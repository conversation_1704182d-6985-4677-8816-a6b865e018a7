<html lang="en"><head>
  <meta charset="UTF-8">
  <title>Jay<PERSON>e Aba-a – Web Designer &amp; Front-End Developer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700|space-grotesk:500" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
  <script src="https://unpkg.com/lucide@latest"></script>
  <style>
    body{font-family:'Inter',sans-serif;background:#0e0e10;color:#e5e7eb;}
    .animate{opacity:0;transform:translateY(40px);transition:all .8s cubic-bezier(.22,1,.36,1);}
    .animate.show{opacity:1;transform:none;}
    .divider{border-top:1px solid rgba(255,255,255,.08);}
    .outline{outline:1px solid rgba(255,255,255,.08);}
    ::selection{background:#6366f1;color:#fff;}
    a{transition:.3s;}
    a:hover{color:#a5b4fc;}
    .gradient-text{background:linear-gradient(90deg,#6366f1,#8b5cf6 60%,#ec4899);-webkit-background-clip:text;color:transparent;}
    input,textarea{background:#1a1a1d;border:1px solid rgba(255,255,255,.1);}
    input:focus,textarea:focus{outline:none;border-color:#6366f1;}
  </style>
<script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex flex-col">
  <!-- NAV -->
  <header class="fixed top-0 inset-x-0 z-50 backdrop-blur-sm bg-black/30">
    <div class="mx-auto max-w-7xl flex items-center justify-between px-6 py-4">
      <h1 class="text-lg tracking-tight font-semibold gradient-text">JK</h1>
      <nav class="hidden sm:flex items-center gap-6 text-sm">
        <a href="#work">Work</a><a href="#services">Services</a><a href="#about">About</a><a href="#contact">Contact</a>
      </nav>
      <div class="sm:flex hidden items-center gap-4">
        <a href="#contact" class="text-xs font-medium px-4 py-2 outline rounded-md hover:bg-white/5">Let’s talk</a>
      </div>
      <button id="menuBtn" class="sm:hidden">
        <i data-lucide="menu" class="stroke-[1.5] w-6 h-6"></i>
      </button>
    </div>
    <!-- mobile -->
    <div id="mobileNav" class="sm:hidden px-6 pt-2 pb-4 space-y-2 hidden">
      <a href="#work" class="block">Work</a>
      <a href="#services" class="block">Services</a>
      <a href="#about" class="block">About</a>
      <a href="#contact" class="block">Contact</a>
    </div>
  </header>

  <!-- HERO -->
  <section class="relative flex flex-col items-center justify-center text-center px-6 pt-40 pb-32">
    <h2 class="text-4xl sm:text-5xl md:text-6xl font-semibold tracking-tight animate delay-[100ms]">Hi, I’m <span class="gradient-text">Jaykee Aba-a</span></h2>
    <p class="mt-6 max-w-xl text-sm sm:text-base opacity-80 animate delay-[250ms]">A freelance website designer crafting memorable, high-performance digital experiences for startups and bold brands worldwide.</p>
    <div class="mt-10 flex gap-4 animate delay-[400ms]">
      <a href="#work" class="px-6 py-3 bg-white text-black rounded-md text-sm font-medium hover:bg-white/90">View work</a>
      <a href="#contact" class="px-6 py-3 outline rounded-md text-sm font-medium hover:bg-white/5">Hire me</a>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- SERVICES -->
  <section id="services" class="mx-auto max-w-7xl px-6 py-24">
    <h3 class="text-3xl font-semibold tracking-tight animate">Services</h3>
    <p class="mt-3 text-sm opacity-80 max-w-md animate delay-[100ms]">End-to-end support: from concept to production and beyond.</p>

    <div class="mt-12 grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
      <div class="p-6 rounded-lg outline hover:outline-white/30 transition animate">
        <i data-lucide="layout" class="stroke-[1.5] w-8 h-8 text-indigo-400"></i>
        <h4 class="mt-4 font-medium">Website Design</h4>
        <p class="mt-2 text-xs opacity-70">High-fidelity Figma concepts focused on conversion and user delight.</p>
      </div>
      <div class="p-6 rounded-lg outline hover:outline-white/30 transition animate delay-[100ms]">
        <i data-lucide="code" class="stroke-[1.5] w-8 h-8 text-indigo-400"></i>
        <h4 class="mt-4 font-medium">Front-End Development</h4>
        <p class="mt-2 text-xs opacity-70">Semantic, accessible code that loads fast and scales as you grow.</p>
      </div>
      <div class="p-6 rounded-lg outline hover:outline-white/30 transition animate delay-[200ms]">
        <i data-lucide="activity" class="stroke-[1.5] w-8 h-8 text-indigo-400"></i>
        <h4 class="mt-4 font-medium">Performance &amp; SEO</h4>
        <p class="mt-2 text-xs opacity-70">Lighthouse 95+ scores paired with technical SEO best practices.</p>
      </div>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- WORK -->
  <section id="work" class="mx-auto max-w-7xl px-6 py-24">
    <h3 class="text-3xl font-semibold tracking-tight animate">Selected Work</h3>
    <p class="mt-3 text-sm opacity-80 max-w-md animate delay-[100ms]">Recent projects that highlight versatility and attention to detail.</p>

    <!-- project cards -->
    <div class="mt-12 grid md:grid-cols-2 gap-10">
      <!-- Card 1 -->
      <div class="animate outline hover:outline-white/30 rounded-lg overflow-hidden">
        <img src="siargao1.png" alt="" class="w-full h-56 object-cover">
        <div class="p-6">
          <h4 class="font-medium">Discover Siargao Island - Web App</h4>
          <p class="mt-2 text-xs opacity-70">A website developed to showcase attractions for visitors who want to travel to Siargao.</p>
          <div class="flex gap-4 mt-4">
            <a href="siargao-details.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://discoversiargao.vercel.app/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
      <!-- Card 2 -->
      <div class="animate delay-[150ms] outline hover:outline-white/30 rounded-lg overflow-hidden">
        <img src="bella1.png" alt="" class="w-full h-56 object-cover">
        <div class="p-6">
          <h4 class="font-medium">Bella Filipina - Web App</h4>
          <p class="mt-2 text-xs opacity-70">A website showcasing authentic Filipino beauty through natural skincare and cosmetics.</p>
          <div class="flex gap-4 mt-4">
            <a href="aurora-details.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://bella-filipina.vercel.app/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
      <!-- Card 3 -->
      <div class="animate delay-[200ms] outline hover:outline-white/30 rounded-lg overflow-hidden">
        <img src="mart.png" alt="" class="w-full h-56 object-cover">
        <div class="p-6">
          <h4 class="font-medium">HyperMart E-commerce Platform</h4>
          <p class="mt-2 text-xs opacity-70">Modern grocery ecommerce app for Hypermart with a fresh green color palette! </p>
          <div class="flex gap-4 mt-4">
            <a href="hypermart-details.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://hypermart-green-grocer.vercel.app/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
      <!-- Card 4 -->
      <div class="animate delay-[250ms] outline hover:outline-white/30 rounded-lg overflow-hidden">
        <img src="dental.png" alt="" class="w-full h-56 object-cover">
        <div class="p-6">
          <h4 class="font-medium">Dental Smyle</h4>
          <p class="mt-2 text-xs opacity-70">A modern dental practice website providing comprehensive dental care services with a focus on patient comfort and advanced technology.</p>
          <div class="flex gap-4 mt-4">
            <a href="dentalsmyle-details.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://dental-smyle.vercel.app/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
      <!-- Card 5 -->
      <div class="animate delay-[300ms] outline hover:outline-white/30 rounded-lg overflow-hidden">
        <img src="brook.png" alt="" class="w-full h-56 object-cover">
        <div class="p-6">
          <h4 class="font-medium">Brook & Bean</h4>
          <p class="mt-2 text-xs opacity-70">Neighborhood Coffee - Locally roasted, open daily. Warm mornings, better coffee — your neighborhood cafe.</p>
          <div class="flex gap-4 mt-4">
            <a href="brookbean.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://brookandbean.vercel.app/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
      <!-- Card 6 -->
      <div class="animate delay-[350ms] outline hover:outline-white/30 rounded-lg overflow-hidden">
        <img src="iron.jpg" alt="" class="w-full h-56 object-cover">
        <div class="p-6">
          <h4 class="font-medium">IronForge</h4>
          <p class="mt-2 text-xs opacity-70">A modern industrial website showcasing strength and precision in metalworking and fabrication services.</p>
          <div class="flex gap-4 mt-4">
            <a href="iron-forge.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://iron-forge.framer.website/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
      <!-- Card 7 -->
      <div class="animate delay-[400ms] outline hover:outline-white/30 rounded-lg overflow-hidden md:col-span-2">
        <img src="medicare.png" alt="" class="w-full h-72 object-cover">
        <div class="p-6">
          <h4 class="font-medium">MediCare</h4>
          <p class="mt-2 text-xs opacity-70">Comprehensive healthcare website providing quality medical services with modern facilities and experienced professionals.</p>
          <div class="flex gap-4 mt-4">
            <a href="medicare.html" class="flex items-center gap-1 text-xs">Case study <i data-lucide="arrow-right" class="stroke-[1.5] w-3 h-3"></i></a>
            <a href="https://medicare-jet.vercel.app/" target="_blank" class="flex items-center gap-1 text-xs opacity-70 hover:opacity-100">Visit website <i data-lucide="external-link" class="stroke-[1.5] w-3 h-3"></i></a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- SKILLS -->
  <section class="mx-auto max-w-7xl px-6 py-24">
    <div class="grid md:grid-cols-2 gap-12 items-center">
      <div>
        <h3 class="text-3xl font-semibold tracking-tight animate">Core Stack</h3>
        <p class="mt-3 text-sm opacity-80 animate delay-[100ms]">I constantly iterate on my toolkit to stay ahead of the curve.</p>
        <ul class="mt-8 space-y-3 text-sm">
          <li class="flex gap-2 items-center animate"><i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400"></i>HTML, CSS, and JavaScript</li>
          <li class="flex gap-2 items-center animate delay-[100ms]"><i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400"></i>React &amp; Next.js</li>
          <li class="flex gap-2 items-center animate delay-[200ms]"><i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400"></i>Tailwind &amp; UI libraries</li>
          <li class="flex gap-2 items-center animate delay-[300ms]"><i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400"></i>Figma and Framer</li>
        </ul>
      </div>
      <div class="animate md:order-last delay-[150ms]">
        <div class="p-6 outline rounded-lg">
          <h4 class="font-medium mb-4">Skill Proficiency (%)</h4>
          <div><canvas id="skillChart" height="220"></canvas></div>
        </div>
      </div>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- TESTIMONIALS -->
  <section class="mx-auto max-w-7xl px-6 py-24">
    <h3 class="text-3xl font-semibold tracking-tight animate">Words from Clients</h3>
    <div class="mt-12 grid md:grid-cols-3 gap-8">
      <figure class="animate outline p-6 rounded-lg">
        <blockquote class="text-sm opacity-90 leading-relaxed">Jaykee elevated our vision and delivered ahead of schedule. The new site doubled our conversions within a month.”</blockquote>
        <figcaption class="mt-4 flex items-center gap-3">
          <img src="https://images.unsplash.com/photo-1550525811-e5869dd03032?auto=format&amp;fit=crop&amp;w=100&amp;q=80" alt="" class="w-8 h-8 rounded-full object-cover">
          <div>
            <p class="text-xs font-medium">Lena Kim</p>
            <p class="text-[10px] opacity-60">CEO, Helio Finance</p>
          </div>
        </figcaption>
      </figure>
      <figure class="animate delay-[100ms] outline p-6 rounded-lg">
        <blockquote class="text-sm opacity-90 leading-relaxed">“Incredible attention to detail. Our team loved the component library and thorough documentation.”</blockquote>
        <figcaption class="mt-4 flex items-center gap-3">
          <img src="https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=1080&amp;q=80" alt="" class="w-8 h-8 rounded-full object-cover">
          <div>
            <p class="text-xs font-medium">Marcus Rivera</p>
            <p class="text-[10px] opacity-60">CTO, Pulse Analytics</p>
          </div>
        </figcaption>
      </figure>
      <figure class="animate delay-[200ms] outline p-6 rounded-lg">
        <blockquote class="text-sm opacity-90 leading-relaxed">“Seamless process from idea to launch. Jaykee's insights saved us weeks of work.”</blockquote>
        <figcaption class="mt-4 flex items-center gap-3">
          <img src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&amp;fit=crop&amp;w=100&amp;q=80" alt="" class="w-8 h-8 rounded-full object-cover">
          <div>
            <p class="text-xs font-medium">Sara Melendez</p>
            <p class="text-[10px] opacity-60">Founder, Aurora Studio</p>
          </div>
        </figcaption>
      </figure>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- ABOUT -->
  <section id="about" class="mx-auto max-w-7xl px-6 py-24">
    <div class="grid md:grid-cols-2 gap-12 items-center">
      <img src="https://images.unsplash.com/photo-1621619856624-42fd193a0661?w=1080&amp;q=80" alt="" class="rounded-lg w-full object-cover h-80 md:h-full outline animate">
      <div class="animate delay-[100ms]">
        <h3 class="text-3xl font-semibold tracking-tight">Background</h3>
        <p class="mt-4 text-sm opacity-80 leading-relaxed">I've spent years refining the art of aesthetics with performance. My mission is simple: <b>help brands </b> stand out through websites that are as fast as they are beautiful.</p>
        <p class="mt-4 text-sm opacity-80 leading-relaxed">When I'm not pushing pixels, you'll find me hiking or brewing specialty coffee.</p>
        <div class="flex gap-4 mt-6">
          <a href="#contact" class="inline-flex items-center gap-2 text-sm font-medium px-4 py-2 outline rounded-md hover:bg-white/5">Let's collaborate <i data-lucide="arrow-right" class="stroke-[1.5] w-4 h-4"></i></a>
          <a href="#" class="inline-flex items-center gap-2 text-sm font-medium px-4 py-2 outline rounded-md hover:bg-white/5">View Resume <i data-lucide="external-link" class="stroke-[1.5] w-4 h-4"></i></a>
        </div>
      </div>
    </div>
  </section>

  <div class="divider mx-6"></div>

  <!-- CONTACT -->
  <section id="contact" class="mx-auto max-w-7xl px-6 py-24">
    <h3 class="text-3xl font-semibold tracking-tight animate">Start a Project</h3>
    <p class="mt-3 text-sm opacity-80 max-w-md animate delay-[100ms]">Tell me about your goals and I’ll reach back within 24 hours.</p>

    <form action="https://formspree.io/f/xovlnvdv" method="POST" class="mt-10 grid md:grid-cols-2 gap-6 animate delay-[200ms]">
      <div class="flex flex-col gap-2">
        <label class="text-xs">Name</label>
        <input type="text" name="name" required="" class="px-4 py-3 rounded-md text-sm">
      </div>
      <div class="flex flex-col gap-2">
        <label class="text-xs">Email</label>
        <input type="email" name="_replyto" required="" class="px-4 py-3 rounded-md text-sm">
      </div>
      <div class="flex flex-col gap-2 md:col-span-2">
        <label class="text-xs">Project details</label>
        <textarea name="message" rows="5" required="" class="px-4 py-3 rounded-md text-sm"></textarea>
      </div>
      <button type="submit" class="md:col-span-2 px-6 py-3 bg-white text-black rounded-md text-sm font-medium hover:bg-white/90">Send message</button>
    </form>

    <div class="mt-6 flex justify-center gap-4">
      <a href="https://github.com/JaykeeAbaa" class="hover:opacity-80"><i data-lucide="github" class="stroke-[1.5] w-6 h-6"></i></a>
      <a href="https://www.linkedin.com/in/jaykee-aba-a-a56bb627b" class="hover:opacity-80"><i data-lucide="linkedin" class="stroke-[1.5] w-6 h-6"></i></a>
      <a href="https://www.instagram.com/jaykeebunn?igsh=MWwxNmh0N2hmbHNoYQ==" class="hover:opacity-80"><i data-lucide="instagram" class="stroke-[1.5] w-6 h-6"></i></a>
      <a href="https://www.facebook.com/khiejay.abaaoffical/" class="hover:opacity-80"><i data-lucide="facebook" class="stroke-[1.5] w-6 h-6"></i></a>
    </div>
  </section>

  <footer class="mt-auto py-10 px-6 text-center text-[11px] opacity-60">
    © <span id="year"></span> Jaykee Aba-a. Built &amp; designed with love.
  </footer>

  <script>
    // Lucide
   lucide.createIcons({attrs:{'stroke-width':1.5}});

    // Mobile nav toggle
    const menuBtn=document.getElementById('menuBtn'), mobileNav=document.getElementById('mobileNav');
    menuBtn.addEventListener('click',()=>mobileNav.classList.toggle('hidden'));

    // Chart.js
    const ctx=document.getElementById('skillChart');
    new Chart(ctx,{type:'radar',data:{labels:['Design','React','Next.js','SEO','Performance','UI Animation'],datasets:[{label:'Proficiency',data:[95,90,88,92,94,86],borderColor:'#6366f1',backgroundColor:'rgba(99,102,241,.2)',borderWidth:2,pointRadius:3}]},options:{scales:{r:{grid:{color:'rgba(255,255,255,.05)'},angleLines:{color:'rgba(255,255,255,.05)'},ticks:{backdropColor:'transparent',color:'#6b7280',font:{size:10}}}},plugins:{legend:{display:false}}}});

    // Scroll animations
    const observer=new IntersectionObserver(entries=>{
      entries.forEach(e=>{if(e.isIntersecting){e.target.classList.add('show');observer.unobserve(e.target);}})
    },{threshold:.12});
    document.querySelectorAll('.animate').forEach(el=>observer.observe(el));

    // year
    document.getElementById('year').textContent=new Date().getFullYear();
  </script>

</body></html>
